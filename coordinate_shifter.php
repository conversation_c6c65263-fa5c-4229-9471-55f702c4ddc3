<?php
/**
 * Coordinate Shifter Class
 * Handles systematic coordinate shifting for Google Places API queries
 * to get varied results from the same pincode area
 */

class CoordinateShifter {
    private $conn;
    private $apiKey;
    
    // Approximate offset for 200 meters in degrees (varies by latitude)
    // 1 degree latitude ≈ 111,000 meters
    // So 200m ≈ 0.0018 degrees
    private $baseOffset = 0.0018;
    
    public function __construct($dbConnection, $googleApiKey) {
        $this->conn = $dbConnection;
        $this->apiKey = $googleApiKey;
    }
    
    /**
     * Get base coordinates for a pincode
     */
    public function getBaseCoordinates($pincode) {
        $geoUrl = "https://maps.googleapis.com/maps/api/geocode/json?address=" . urlencode($pincode) . "&key=" . $this->apiKey;
        $geoData = json_decode(file_get_contents($geoUrl), true);
        
        if ($geoData['status'] !== 'OK') {
            throw new Exception("Invalid pincode or API limit exceeded");
        }
        
        $location = $geoData['results'][0]['geometry']['location'];
        return [
            'lat' => $location['lat'],
            'lng' => $location['lng']
        ];
    }
    
    /**
     * Initialize coordinate shifts for a pincode
     */
    public function initializeShiftsForPincode($pincode) {
        try {
            $baseCoords = $this->getBaseCoordinates($pincode);
            
            // Define shift patterns (200m grid around center)
            $shiftPatterns = [
                ['shift' => 0, 'lat_offset' => 0.0000, 'lng_offset' => 0.0000],      // Center
                ['shift' => 1, 'lat_offset' => 0.0018, 'lng_offset' => 0.0000],     // North
                ['shift' => 2, 'lat_offset' => 0.0000, 'lng_offset' => 0.0018],     // East
                ['shift' => 3, 'lat_offset' => -0.0018, 'lng_offset' => 0.0000],    // South
                ['shift' => 4, 'lat_offset' => 0.0000, 'lng_offset' => -0.0018],    // West
                ['shift' => 5, 'lat_offset' => 0.0018, 'lng_offset' => 0.0018],     // Northeast
                ['shift' => 6, 'lat_offset' => -0.0018, 'lng_offset' => 0.0018],    // Southeast
                ['shift' => 7, 'lat_offset' => -0.0018, 'lng_offset' => -0.0018],   // Southwest
                ['shift' => 8, 'lat_offset' => 0.0018, 'lng_offset' => -0.0018],    // Northwest
                ['shift' => 9, 'lat_offset' => 0.0036, 'lng_offset' => 0.0000],     // North 400m
                ['shift' => 10, 'lat_offset' => 0.0000, 'lng_offset' => 0.0036],    // East 400m
                ['shift' => 11, 'lat_offset' => -0.0036, 'lng_offset' => 0.0000],   // South 400m
                ['shift' => 12, 'lat_offset' => 0.0000, 'lng_offset' => -0.0036],   // West 400m
                ['shift' => 13, 'lat_offset' => 0.0036, 'lng_offset' => 0.0036],    // Northeast 400m
                ['shift' => 14, 'lat_offset' => -0.0036, 'lng_offset' => 0.0036],   // Southeast 400m
                ['shift' => 15, 'lat_offset' => -0.0036, 'lng_offset' => -0.0036],  // Southwest 400m
                ['shift' => 16, 'lat_offset' => 0.0036, 'lng_offset' => -0.0036],   // Northwest 400m
            ];
            
            // Insert shift patterns for this pincode
            $stmt = $this->conn->prepare("INSERT IGNORE INTO coordinate_shifts (pincode, shift_number, lat_offset, lng_offset, base_lat, base_lng) VALUES (?, ?, ?, ?, ?, ?)");
            
            foreach ($shiftPatterns as $pattern) {
                $stmt->bind_param("sidddd", 
                    $pincode, 
                    $pattern['shift'], 
                    $pattern['lat_offset'], 
                    $pattern['lng_offset'],
                    $baseCoords['lat'],
                    $baseCoords['lng']
                );
                $stmt->execute();
            }
            
            $stmt->close();
            return true;
            
        } catch (Exception $e) {
            error_log("Error initializing shifts for pincode $pincode: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get next coordinate shift for a pincode
     */
    public function getNextShiftCoordinates($pincode) {
        // First, ensure shifts are initialized for this pincode
        $this->initializeShiftsForPincode($pincode);
        
        // Get the next unused shift or the oldest used one
        $query = "SELECT * FROM coordinate_shifts 
                  WHERE pincode = ? 
                  ORDER BY last_used ASC, shift_number ASC 
                  LIMIT 1";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bind_param("s", $pincode);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($row = $result->fetch_assoc()) {
            // Calculate actual coordinates
            $actualLat = $row['base_lat'] + $row['lat_offset'];
            $actualLng = $row['base_lng'] + $row['lng_offset'];
            
            // Update last_used timestamp
            $updateStmt = $this->conn->prepare("UPDATE coordinate_shifts SET last_used = NOW() WHERE id = ?");
            $updateStmt->bind_param("i", $row['id']);
            $updateStmt->execute();
            $updateStmt->close();
            
            $stmt->close();
            
            return [
                'lat' => $actualLat,
                'lng' => $actualLng,
                'shift_number' => $row['shift_number'],
                'base_lat' => $row['base_lat'],
                'base_lng' => $row['base_lng'],
                'offset_info' => "Shift #{$row['shift_number']} (Offset: {$row['lat_offset']}, {$row['lng_offset']})"
            ];
        }
        
        $stmt->close();
        throw new Exception("No coordinate shifts available for pincode: $pincode");
    }
    
    /**
     * Track daily collection stats
     */
    public function updateDailyStats($pincode, $recordsAdded = 0, $apiCallsMade = 1) {
        $today = date('Y-m-d');
        
        // Insert or update daily stats
        $query = "INSERT INTO daily_collection_stats (date, pincode, records_collected, api_calls_made) 
                  VALUES (?, ?, ?, ?) 
                  ON DUPLICATE KEY UPDATE 
                  records_collected = records_collected + VALUES(records_collected),
                  api_calls_made = api_calls_made + VALUES(api_calls_made),
                  updated_at = NOW()";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bind_param("ssii", $today, $pincode, $recordsAdded, $apiCallsMade);
        $stmt->execute();
        $stmt->close();
    }
    
    /**
     * Check if daily limit reached
     */
    public function isDailyLimitReached($pincode, $dailyLimit = 500) {
        $today = date('Y-m-d');
        
        $query = "SELECT records_collected FROM daily_collection_stats 
                  WHERE date = ? AND pincode = ?";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bind_param("ss", $today, $pincode);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($row = $result->fetch_assoc()) {
            $stmt->close();
            return $row['records_collected'] >= $dailyLimit;
        }
        
        $stmt->close();
        return false;
    }
    
    /**
     * Get daily stats for a pincode
     */
    public function getDailyStats($pincode) {
        $today = date('Y-m-d');
        
        $query = "SELECT * FROM daily_collection_stats 
                  WHERE date = ? AND pincode = ?";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bind_param("ss", $today, $pincode);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($row = $result->fetch_assoc()) {
            $stmt->close();
            return $row;
        }
        
        $stmt->close();
        return [
            'records_collected' => 0,
            'api_calls_made' => 0,
            'target_limit' => 500,
            'status' => 'active'
        ];
    }
}
?>
