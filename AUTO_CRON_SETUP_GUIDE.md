# Auto Cron Runner Setup Guide

## 🎯 **What This Does**
The auto-runner script checks your database every 15 minutes and automatically runs cron jobs based on their individual frequency settings.

## 📁 **Files Created**
- `auto-cron-runner.php` - Main auto-runner script
- `cron-runner.log` - Log file (created automatically)

## 🚀 **Setup Steps**

### **Step 1: Test the Script Manually**
First, test if the script works:
```
https://yourdomain.com/auto-cron-runner.php?manual_run=1
```

You should see output like:
```
[2024-01-08 15:30:00] === Auto Cron Runner Started ===
[2024-01-08 15:30:01] Found 1 active cron jobs
[2024-01-08 15:30:01] Processing job ID 1: 400705 (Vashi) - Frequency: 15 min
[2024-01-08 15:30:01] Job never run before - scheduling for execution
[2024-01-08 15:30:01] Executing job for 400705...
[2024-01-08 15:30:02] Using coordinates: Lat 19.0728, Lng 73.0117 (Shift #1)
[2024-01-08 15:30:15] Job completed - collected 12 new records
[2024-01-08 15:30:15] === Auto Cron Runner Completed ===
```

### **Step 2: Set Up Server Cron Job**

#### **Option A: cPanel Cron Jobs**
1. Login to your cPanel
2. Go to "Cron Jobs"
3. Add new cron job:
   - **Minute**: `*/15` (every 15 minutes)
   - **Hour**: `*` (every hour)
   - **Day**: `*` (every day)
   - **Month**: `*` (every month)
   - **Weekday**: `*` (every weekday)
   - **Command**: `/usr/bin/php /home/<USER>/public_html/auto-cron-runner.php`

#### **Option B: SSH/Terminal**
```bash
# Edit crontab
crontab -e

# Add this line:
*/15 * * * * /usr/bin/php /path/to/your/website/auto-cron-runner.php
```

#### **Option C: Hosting Control Panel**
Most hosting providers have a "Cron Jobs" or "Scheduled Tasks" section where you can add:
- **Frequency**: Every 15 minutes
- **Command**: `php auto-cron-runner.php`
- **Path**: Your website directory

## 🔧 **How It Works**

### **Smart Timing Logic**
```php
// Example: Job set to run every 15 minutes
- Last run: 14:30
- Current time: 14:44
- Time difference: 14 minutes
- Action: Skip (need 15+ minutes)

- Current time: 14:46  
- Time difference: 16 minutes
- Action: Execute job
```

### **Daily Limit Management**
- Checks if daily target reached before running
- Updates job status to 'target_reached' when limit hit
- Resets automatically next day

### **Coordinate Shifting**
- Uses next available coordinate shift for each execution
- Ensures varied results from same pincode area
- Tracks shift usage automatically

## 📊 **Monitoring & Logs**

### **Log File Location**
`cron-runner.log` in your website directory

### **Log Content**
```
[2024-01-08 15:30:00] === Auto Cron Runner Started ===
[2024-01-08 15:30:01] Found 1 active cron jobs
[2024-01-08 15:30:01] Processing job ID 1: 400705 (Vashi) - Frequency: 15 min
[2024-01-08 15:30:01] Time since last run: 16.2 min - scheduling for execution
[2024-01-08 15:30:01] Executing job for 400705...
[2024-01-08 15:30:02] Using coordinates: Lat 19.0728, Lng 73.0117 (Shift #2)
[2024-01-08 15:30:15] Job completed - collected 8 new records
[2024-01-08 15:30:15] Jobs processed: 1
[2024-01-08 15:30:15] Total records collected: 8
```

### **Check Job Status**
In Cron Manager, you'll see:
- **Last Run**: Updated automatically
- **Total Collected**: Increases with each run
- **Status**: Changes to 'target_reached' when daily limit hit

## ⚙️ **Configuration**

### **Frequency Settings**
Each job runs based on its individual frequency:
- **15 min job**: Runs every 15 minutes
- **30 min job**: Runs every 30 minutes  
- **1 hr job**: Runs every hour
- **Daily job**: Runs once per day

### **Safety Features**
- **API Rate Limiting**: 2-second delay between jobs
- **Error Handling**: Continues if one job fails
- **Duplicate Prevention**: Checks existing place_ids
- **Daily Limits**: Stops at configured targets

## 🔍 **Troubleshooting**

### **Script Not Running**
1. Check server cron job is set up correctly
2. Verify PHP path: `/usr/bin/php` or `/usr/local/bin/php`
3. Check file permissions (755 for script)
4. Test manual run first

### **No Records Collected**
1. Check API key is valid
2. Verify database connection
3. Check daily limits not reached
4. Review log file for errors

### **Jobs Not Triggering**
1. Verify jobs are 'active' status
2. Check frequency settings
3. Look at 'Last Run' times
4. Review cron-runner.log

## 📈 **Expected Results**

### **For Your 15-minute Job (400705)**
- **Runs**: Every 15 minutes
- **Daily Executions**: ~96 times (24 hours × 4)
- **Records per Run**: ~5-15 (varies by area)
- **Daily Total**: ~500 records (stops at target)

### **Performance**
- **API Calls**: Distributed throughout day
- **Server Load**: Minimal (runs in background)
- **Data Quality**: High (no websites, coordinate shifted)

## ✅ **Verification**

After setup, verify it's working:
1. **Wait 15+ minutes** after setup
2. **Check Cron Manager** - "Last Run" should update
3. **Check Log File** - Should show execution logs
4. **Check Results** - New records in database

Your cron job will now run automatically every 15 minutes as configured! 🎉
