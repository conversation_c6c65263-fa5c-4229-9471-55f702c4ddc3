<?php
// Update cron_job_configs table to add run_frequency column
require 'db_connect.php';

echo "<h2>Updating Cron Job Configs Table...</h2>\n";

// Check if run_frequency column already exists
$checkColumn = $conn->query("SHOW COLUMNS FROM cron_job_configs LIKE 'run_frequency'");

if ($checkColumn->num_rows == 0) {
    echo "<p>Adding run_frequency column...</p>\n";
    
    // Add the column
    $sql1 = "ALTER TABLE cron_job_configs 
             ADD COLUMN run_frequency INT DEFAULT 30 COMMENT 'Run frequency in minutes' 
             AFTER daily_target";
    
    if ($conn->query($sql1)) {
        echo "<p style='color: green;'>✓ run_frequency column added successfully</p>\n";
        
        // Update existing records
        $sql2 = "UPDATE cron_job_configs 
                 SET run_frequency = 30 
                 WHERE run_frequency IS NULL";
        
        if ($conn->query($sql2)) {
            echo "<p style='color: green;'>✓ Existing records updated with default frequency</p>\n";
        } else {
            echo "<p style='color: red;'>✗ Error updating existing records: " . $conn->error . "</p>\n";
        }
        
    } else {
        echo "<p style='color: red;'>✗ Error adding column: " . $conn->error . "</p>\n";
    }
} else {
    echo "<p style='color: blue;'>ℹ run_frequency column already exists</p>\n";
}

echo "<h3>Update Complete!</h3>\n";
echo "<p><a href='cron-manager.php'>→ Go to Cron Manager</a></p>\n";

$conn->close();
?>
