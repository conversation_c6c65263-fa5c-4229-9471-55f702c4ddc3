<?php
// <PERSON><PERSON><PERSON> to update existing users.json and add role field to BM999

echo "<h2>Updating User Roles...</h2>";

$usersFile = 'users.json';

if (!file_exists($usersFile)) {
    echo "<p style='color: red;'>❌ users.json file not found!</p>";
    echo "<p>Creating new users.json with BM999 as admin...</p>";
    
    // Create BM999 as admin user
    $users = [
        [
            'id' => 'user_bm999_admin',
            'username' => 'BM999',
            'email' => '<EMAIL>',
            'password' => password_hash('Welcome@1', PASSWORD_DEFAULT),
            'role' => 'admin',
            'created_at' => date('Y-m-d H:i:s'),
            'last_login' => null,
            'status' => 'active'
        ]
    ];
    
    if (file_put_contents($usersFile, json_encode($users, JSON_PRETTY_PRINT))) {
        echo "<p style='color: green;'>✅ Created users.json with BM999 as admin!</p>";
    } else {
        echo "<p style='color: red;'>❌ Failed to create users.json</p>";
        exit;
    }
} else {
    // Load existing users
    $usersData = file_get_contents($usersFile);
    $users = json_decode($usersData, true);
    
    if (!$users) {
        echo "<p style='color: red;'>❌ Failed to parse users.json</p>";
        exit;
    }
    
    echo "<p>📊 Found " . count($users) . " users in the system</p>";
    
    // Update users to add role field
    $updated = false;
    foreach ($users as &$user) {
        if (!isset($user['role'])) {
            // Set BM999 as admin, others as regular users
            if ($user['username'] === 'BM999') {
                $user['role'] = 'admin';
                echo "<p style='color: green;'>✅ Set BM999 as admin</p>";
            } else {
                $user['role'] = 'user';
                echo "<p style='color: blue;'>ℹ️ Set " . htmlspecialchars($user['username']) . " as regular user</p>";
            }
            $updated = true;
        } else {
            echo "<p>ℹ️ " . htmlspecialchars($user['username']) . " already has role: " . $user['role'] . "</p>";
        }
    }
    
    if ($updated) {
        // Save updated users
        if (file_put_contents($usersFile, json_encode($users, JSON_PRETTY_PRINT))) {
            echo "<p style='color: green;'>✅ Updated users.json with role information!</p>";
        } else {
            echo "<p style='color: red;'>❌ Failed to save updated users.json</p>";
            exit;
        }
    } else {
        echo "<p>ℹ️ No updates needed - all users already have roles</p>";
    }
}

// Display current users
echo "<h3>Current Users:</h3>";
$finalUsers = json_decode(file_get_contents($usersFile), true);
echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
echo "<tr style='background: #f0f0f0;'><th>Username</th><th>Email</th><th>Role</th><th>Status</th><th>Created</th></tr>";
foreach ($finalUsers as $user) {
    $roleColor = ($user['role'] === 'admin') ? 'color: red; font-weight: bold;' : 'color: blue;';
    echo "<tr>";
    echo "<td>" . htmlspecialchars($user['username']) . "</td>";
    echo "<td>" . htmlspecialchars($user['email']) . "</td>";
    echo "<td style='$roleColor'>" . strtoupper($user['role']) . "</td>";
    echo "<td>" . $user['status'] . "</td>";
    echo "<td>" . $user['created_at'] . "</td>";
    echo "</tr>";
}
echo "</table>";

echo "<hr>";
echo "<h3>Next Steps:</h3>";
echo "<ol>";
echo "<li><a href='login.php'>Login as BM999</a> (admin user)</li>";
echo "<li><a href='manage-users.php'>View User Management</a> (admin only)</li>";
echo "<li><a href='register.php'>Create New Users</a> (admin only)</li>";
echo "<li>Delete this file (update_user_roles.php) for security</li>";
echo "</ol>";
?>

<style>
body { font-family: Arial, sans-serif; padding: 20px; max-width: 800px; margin: 0 auto; }
h2, h3 { color: #333; }
p { line-height: 1.6; }
table { width: 100%; }
th, td { padding: 8px; text-align: left; }
a { color: #007cba; text-decoration: none; }
a:hover { text-decoration: underline; }
</style>
