# Business Finder - Enhanced Version

A modern web application to find businesses without websites using pincode and keyword combinations.

## 🚀 New Features

### ✅ **Login-Protected Search System**
- Search functionality requires user authentication
- All search results saved to database
- User-specific access control
- Professional user management

### ✅ **Professional Header & Navigation**
- Modern responsive header with gradient design
- User authentication status display
- Clean navigation menu with icons
- Mobile-friendly responsive design

### ✅ **JSON-Based Authentication System**
- User registration and login
- Password hashing for security
- Session management
- User data stored in `users.json`

### ✅ **Complete Page Structure**
- **Home Page** (`index.php`) - Landing page (different content for logged-in users)
- **Search Page** (`search.php`) - Protected search interface (login required)
- **Results Page** (`fetch.php`) - Enhanced results display (login required)
- **View Results** (`view-results.php`) - Browse saved database results (login required)
- **Dashboard** (`dashboard.php`) - User dashboard (login required)
- **Admin Panel** (`admin.php`) - Analytics and statistics (login required)
- **About Page** (`about.php`) - Information about the platform (public)
- **Contact Page** (`contact.php`) - Contact information and FAQ (public)
- **Login** - User authentication page (public)

## 🔧 Installation & Setup

### Prerequisites
- PHP 7.4 or higher
- MySQL database
- Web server (Apache/Nginx) or PHP built-in server
- Google Places API key

### Quick Start
1. **Upload files** to your web server
2. **Setup database** using the SQL in `SETUP.md`
3. **Configure API Key** in `config.php`
4. **Set file permissions** for `users.json`
5. **Login with provided credentials** and start searching

### Detailed Setup
See `SETUP.md` for complete installation instructions.

## 📁 File Structure

```
├── index.php              # Home page
├── search.php             # Search interface
├── fetch.php              # Search results
├── login.php              # User login
├── logout.php             # Logout handler
├── dashboard.php          # User dashboard
├── about.php              # About page
├── contact.php            # Contact page
├── view-results.php       # Database results viewer
├── header.php             # Common header
├── footer.php             # Common footer
├── config.php             # API configuration
├── db_connect.php         # Database connection
├── users.json             # User data storage
├── manual_setup.sql       # Database setup script
└── README.md              # This file
```

## 🎯 Key Features

### **Enhanced Search**
- **Location-based:** Search by Indian pincode
- **Keyword filtering:** Find specific business types
- **Combined search:** Pincode + keywords for targeted results
- **Real-time data:** Google Places API integration

### **User Management**
- **Multi-user system:** Role-based access control
- **Admin users:** Can create and manage other users
- **Public registration:** Disabled for security
- **Role-based features:** Different access levels

### **Modern UI/UX**
- **Responsive design:** Works on all devices
- **Professional styling:** Modern gradient themes
- **Intuitive navigation:** Easy-to-use interface
- **Loading states:** User feedback during searches

### **Business Intelligence**
- **No-website filter:** Focus on businesses without websites
- **Contact information:** Phone numbers and addresses
- **Business categories:** Industry classification
- **Rating information:** Google ratings when available

## 🔍 How to Use

### **For Public Visitors**
1. Visit the home page
2. View About and Contact pages
3. Must login to access search functionality

### **For Logged-in User**
1. Login with provided credentials
2. Access the dashboard for overview
3. Use the search functionality to find businesses
4. View and manage all saved search results

### **Search Examples**
- **Restaurants in Delhi:** Pincode: `110001`, Keywords: `restaurant, cafe, food`
- **Medical clinics in Mumbai:** Pincode: `400001`, Keywords: `clinic, doctor, medical`
- **All businesses in Bangalore:** Pincode: `560001`, Keywords: (leave empty)

## 🛠️ Technical Details

### **Authentication System**
- **Storage:** JSON file (`users.json`)
- **Security:** Password hashing with PHP's `password_hash()`
- **Sessions:** PHP session management
- **Validation:** Input validation and sanitization

### **Database Integration**
- **MySQL:** Business data storage
- **Duplicate prevention:** Place ID based deduplication
- **Efficient queries:** Indexed searches and pagination
- **Data persistence:** All search results saved automatically

### **API Integration**
- **Google Places API:** Business data source
- **Geocoding:** Pincode to coordinates conversion
- **Rate limiting:** Respects API limits
- **Error handling:** Graceful failure management

## 🔒 Security Features

- **Password hashing:** Secure password storage
- **Session management:** Secure user sessions
- **Input validation:** XSS and injection protection
- **CSRF protection:** Form security (recommended to add)

## 🚀 Future Enhancements

- **Save search results:** Bookmark businesses
- **Export functionality:** CSV/Excel downloads
- **Advanced filtering:** More search options
- **Team collaboration:** Share searches
- **API access:** Developer API
- **Analytics dashboard:** Usage statistics

## 📞 Support

For questions or issues:
- **Email:** <EMAIL>
- **Phone:** +91 98765 43210
- **Documentation:** Check the About and Contact pages

## 📄 License

This project is for educational and commercial use. Please respect Google Places API terms of service.

---

**Ready to find businesses without websites?** Start searching now! 🔍
