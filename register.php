<?php
session_start();

// Check if user is logged in and is admin
if (!isset($_SESSION['user_logged_in']) || $_SESSION['user_logged_in'] !== true) {
    header("Location: login.php?redirect=register.php");
    exit;
}

// Check if user has admin role
if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin') {
    header("Location: dashboard.php?error=access_denied");
    exit;
}

$pageTitle = "Create New User - Business Finder";

// Handle registration form submission
$registerError = '';
$registerSuccess = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = trim($_POST['username'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $password = trim($_POST['password'] ?? '');
    $confirmPassword = trim($_POST['confirm_password'] ?? '');
    $role = trim($_POST['role'] ?? 'user');
    
    // Validation
    if (empty($username) || empty($email) || empty($password) || empty($confirmPassword)) {
        $registerError = 'All fields are required.';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $registerError = 'Please enter a valid email address.';
    } elseif (strlen($password) < 6) {
        $registerError = 'Password must be at least 6 characters long.';
    } elseif ($password !== $confirmPassword) {
        $registerError = 'Passwords do not match.';
    } elseif (!in_array($role, ['admin', 'user'])) {
        $registerError = 'Invalid role selected.';
    } else {
        // Load existing users
        $usersFile = 'users.json';
        $users = [];
        
        if (file_exists($usersFile)) {
            $usersData = file_get_contents($usersFile);
            $users = json_decode($usersData, true) ?? [];
        }
        
        // Check if username or email already exists
        $userExists = false;
        foreach ($users as $user) {
            if ($user['username'] === $username || $user['email'] === $email) {
                $userExists = true;
                break;
            }
        }
        
        if ($userExists) {
            $registerError = 'Username or email already exists.';
        } else {
            // Create new user
            $newUser = [
                'id' => uniqid('user_'),
                'username' => $username,
                'email' => $email,
                'password' => password_hash($password, PASSWORD_DEFAULT),
                'role' => $role,
                'created_at' => date('Y-m-d H:i:s'),
                'created_by' => $_SESSION['username'],
                'last_login' => null,
                'status' => 'active'
            ];
            
            $users[] = $newUser;
            
            // Save to JSON file
            if (file_put_contents($usersFile, json_encode($users, JSON_PRETTY_PRINT))) {
                $registerSuccess = "User '$username' created successfully with role '$role'.";
                // Clear form data
                $_POST = [];
            } else {
                $registerError = 'Failed to save user data. Please try again.';
            }
        }
    }
}

include 'header.php';
?>

<style>
    .register-container {
        max-width: 500px;
        margin: 50px auto;
        background: white;
        padding: 40px;
        border-radius: 10px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }
    
    .register-header {
        text-align: center;
        margin-bottom: 30px;
    }
    
    .register-header h2 {
        color: #333;
        margin-bottom: 10px;
    }
    
    .register-header p {
        color: #666;
        font-size: 14px;
    }
    
    .form-group {
        margin-bottom: 20px;
    }
    
    .form-group label {
        display: block;
        margin-bottom: 5px;
        color: #333;
        font-weight: 500;
    }
    
    .form-group input, .form-group select {
        width: 100%;
        padding: 12px;
        border: 2px solid #ddd;
        border-radius: 5px;
        font-size: 16px;
        transition: border-color 0.3s;
    }
    
    .form-group input:focus, .form-group select:focus {
        outline: none;
        border-color: #667eea;
    }
    
    .btn-register {
        width: 100%;
        padding: 12px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        border-radius: 5px;
        font-size: 16px;
        font-weight: 500;
        cursor: pointer;
        transition: transform 0.2s;
    }
    
    .btn-register:hover {
        transform: translateY(-2px);
    }
    
    .alert {
        padding: 12px;
        border-radius: 5px;
        margin-bottom: 20px;
    }
    
    .alert-error {
        background-color: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }
    
    .alert-success {
        background-color: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }
    
    .back-link {
        text-align: center;
        margin-top: 20px;
        padding-top: 20px;
        border-top: 1px solid #eee;
    }
    
    .back-link a {
        color: #667eea;
        text-decoration: none;
        font-weight: 500;
    }
    
    .back-link a:hover {
        text-decoration: underline;
    }
    
    .admin-info {
        background: #e8f4fd;
        padding: 15px;
        border-radius: 5px;
        margin-bottom: 20px;
        border-left: 4px solid #007cba;
    }
    
    .admin-info h4 {
        color: #333;
        margin-bottom: 10px;
    }
    
    .admin-info p {
        color: #666;
        font-size: 14px;
        margin: 0;
    }
</style>

<div class="register-container">
    <div class="register-header">
        <h2>👥 Create New User</h2>
        <p>Administrator: Create a new user account for the system</p>
    </div>
    
    <div class="admin-info">
        <h4>🔐 Admin Access</h4>
        <p>You are logged in as: <strong><?= htmlspecialchars($_SESSION['username']) ?></strong></p>
        <p>Only administrators can create new user accounts.</p>
    </div>
    
    <?php if ($registerError): ?>
        <div class="alert alert-error">
            <?= htmlspecialchars($registerError) ?>
        </div>
    <?php endif; ?>
    
    <?php if ($registerSuccess): ?>
        <div class="alert alert-success">
            <?= htmlspecialchars($registerSuccess) ?>
        </div>
    <?php endif; ?>
    
    <form method="POST" action="">
        <div class="form-group">
            <label for="username">Username:</label>
            <input type="text" id="username" name="username" required 
                   value="<?= htmlspecialchars($_POST['username'] ?? '') ?>"
                   placeholder="Enter username">
        </div>
        
        <div class="form-group">
            <label for="email">Email:</label>
            <input type="email" id="email" name="email" required 
                   value="<?= htmlspecialchars($_POST['email'] ?? '') ?>"
                   placeholder="Enter email address">
        </div>
        
        <div class="form-group">
            <label for="role">User Role:</label>
            <select id="role" name="role" required>
                <option value="user" <?= ($_POST['role'] ?? 'user') === 'user' ? 'selected' : '' ?>>Regular User</option>
                <option value="admin" <?= ($_POST['role'] ?? '') === 'admin' ? 'selected' : '' ?>>Administrator</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="password">Password:</label>
            <input type="password" id="password" name="password" required 
                   placeholder="Create a password">
            <small style="color: #666; font-size: 12px; margin-top: 5px; display: block;">
                Password must be at least 6 characters long
            </small>
        </div>
        
        <div class="form-group">
            <label for="confirm_password">Confirm Password:</label>
            <input type="password" id="confirm_password" name="confirm_password" required 
                   placeholder="Confirm the password">
        </div>
        
        <button type="submit" class="btn-register">Create User Account</button>
    </form>
    
    <div class="back-link">
        <p><a href="admin.php">← Back to Admin Panel</a> | <a href="dashboard.php">Dashboard</a></p>
    </div>
</div>

<?php include 'footer.php'; ?>
