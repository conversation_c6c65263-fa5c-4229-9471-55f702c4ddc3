<?php
session_start();

// Check if user is logged in and is admin
if (!isset($_SESSION['user_logged_in']) || $_SESSION['user_logged_in'] !== true) {
    header("Location: login.php?redirect=cron-manager.php");
    exit;
}

// Check if user has admin role
if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin') {
    header("Location: dashboard.php?error=access_denied");
    exit;
}

require 'db_connect.php';
require 'coordinate_shifter.php';

$pageTitle = "Cron Job Manager - Business Finder";

// Handle form submissions
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'create_job') {
        $pincode = trim($_POST['pincode'] ?? '');
        $area_name = trim($_POST['area_name'] ?? '');
        $keywords = trim($_POST['keywords'] ?? '');
        $daily_target = (int)($_POST['daily_target'] ?? 500);
        $run_frequency = (int)($_POST['run_frequency'] ?? 30);

        if (!empty($pincode) && preg_match('/^\d{6}$/', $pincode)) {
            $stmt = $conn->prepare("INSERT INTO cron_job_configs (pincode, area_name, keywords, daily_target, run_frequency, created_by) VALUES (?, ?, ?, ?, ?, ?)");
            $stmt->bind_param("sssiss", $pincode, $area_name, $keywords, $daily_target, $run_frequency, $_SESSION['username']);
            
            if ($stmt->execute()) {
                $message = "Cron job created successfully for pincode $pincode";
                $messageType = 'success';
            } else {
                $message = "Error creating cron job: " . $conn->error;
                $messageType = 'error';
            }
            $stmt->close();
        } else {
            $message = "Please enter a valid 6-digit pincode";
            $messageType = 'error';
        }
    }
    
    elseif ($action === 'toggle_status') {
        $job_id = (int)($_POST['job_id'] ?? 0);
        $new_status = $_POST['new_status'] ?? '';

        if ($job_id > 0 && in_array($new_status, ['active', 'inactive'])) {
            $stmt = $conn->prepare("UPDATE cron_job_configs SET status = ? WHERE id = ?");
            $stmt->bind_param("si", $new_status, $job_id);

            if ($stmt->execute()) {
                $message = "Job status updated successfully";
                $messageType = 'success';
            } else {
                $message = "Error updating job status";
                $messageType = 'error';
            }
            $stmt->close();
        }
    }

    elseif ($action === 'delete_job') {
        $job_id = (int)($_POST['job_id'] ?? 0);

        if ($job_id > 0) {
            // Get job details for confirmation message
            $stmt = $conn->prepare("SELECT pincode, area_name FROM cron_job_configs WHERE id = ?");
            $stmt->bind_param("i", $job_id);
            $stmt->execute();
            $result = $stmt->get_result();

            if ($job = $result->fetch_assoc()) {
                $jobInfo = $job['pincode'] . ($job['area_name'] ? ' (' . $job['area_name'] . ')' : '');
                $stmt->close();

                // Delete the job
                $deleteStmt = $conn->prepare("DELETE FROM cron_job_configs WHERE id = ?");
                $deleteStmt->bind_param("i", $job_id);

                if ($deleteStmt->execute()) {
                    $message = "Cron job for $jobInfo deleted successfully";
                    $messageType = 'success';
                } else {
                    $message = "Error deleting cron job: " . $conn->error;
                    $messageType = 'error';
                }
                $deleteStmt->close();
            } else {
                $stmt->close();
                $message = "Job not found";
                $messageType = 'error';
            }
        } else {
            $message = "Invalid job ID";
            $messageType = 'error';
        }
    }
    
    elseif ($action === 'run_manual') {
        $job_id = (int)($_POST['job_id'] ?? 0);
        
        if ($job_id > 0) {
            // Get job details
            $stmt = $conn->prepare("SELECT * FROM cron_job_configs WHERE id = ?");
            $stmt->bind_param("i", $job_id);
            $stmt->execute();
            $result = $stmt->get_result();
            
            if ($job = $result->fetch_assoc()) {
                $stmt->close();
                
                // Redirect to fetch.php with coordinate shifting enabled
                $_SESSION['manual_cron_job'] = $job;
                header("Location: manual-fetch.php?job_id=$job_id");
                exit;
            } else {
                $message = "Job not found";
                $messageType = 'error';
            }
        }
    }
}

// Get all cron jobs
$cronJobs = $conn->query("SELECT * FROM cron_job_configs ORDER BY created_at DESC");

// Get today's stats
$today = date('Y-m-d');
$dailyStats = $conn->query("SELECT pincode, SUM(records_collected) as total_records, SUM(api_calls_made) as total_calls FROM daily_collection_stats WHERE date = '$today' GROUP BY pincode");

include 'header.php';
?>

<style>
    .cron-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
    }
    
    .page-header {
        text-align: center;
        margin-bottom: 30px;
        padding: 30px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
    }
    
    .alert {
        padding: 15px;
        margin-bottom: 20px;
        border-radius: 8px;
        font-weight: 500;
    }
    
    .alert-success {
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }
    
    .alert-error {
        background: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }
    
    .card {
        background: white;
        padding: 25px;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin-bottom: 30px;
    }
    
    .form-group {
        margin-bottom: 20px;
    }
    
    .form-group label {
        display: block;
        margin-bottom: 8px;
        font-weight: 500;
        color: #333;
    }
    
    .form-group input, .form-group textarea, .form-group select {
        width: 100%;
        padding: 12px;
        border: 2px solid #e1e5e9;
        border-radius: 8px;
        font-size: 16px;
        transition: border-color 0.3s;
        background-color: white;
    }

    .form-group input:focus, .form-group textarea:focus, .form-group select:focus {
        outline: none;
        border-color: #667eea;
    }

    .form-group select {
        cursor: pointer;
    }
    
    .btn {
        padding: 10px 20px;
        border: none;
        border-radius: 6px;
        cursor: pointer;
        font-weight: 500;
        text-decoration: none;
        display: inline-block;
        margin-right: 10px;
        margin-bottom: 10px;
    }
    
    .btn-primary {
        background: #007cba;
        color: white;
    }
    
    .btn-success {
        background: #28a745;
        color: white;
    }
    
    .btn-warning {
        background: #ffc107;
        color: #212529;
    }
    
    .btn-danger {
        background: #dc3545;
        color: white;
    }
    
    .btn-secondary {
        background: #6c757d;
        color: white;
    }
    
    .jobs-table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 20px;
    }
    
    .jobs-table th, .jobs-table td {
        padding: 12px;
        text-align: left;
        border-bottom: 1px solid #ddd;
    }
    
    .jobs-table th {
        background: #f8f9fa;
        font-weight: 600;
    }
    
    .status-badge {
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 500;
    }
    
    .status-active {
        background: #d4edda;
        color: #155724;
    }
    
    .status-inactive {
        background: #f8d7da;
        color: #721c24;
    }
    
    .status-target_reached {
        background: #fff3cd;
        color: #856404;
    }
    
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }
    
    .stat-card {
        background: white;
        padding: 20px;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        text-align: center;
    }
    
    .stat-number {
        font-size: 2em;
        font-weight: bold;
        color: #007cba;
    }
    
    .stat-label {
        color: #666;
        margin-top: 5px;
    }
</style>

<div class="cron-container">
    <div class="page-header">
        <h1>⚙️ Cron Job Manager</h1>
        <p>Configure and manage automated business data collection with coordinate shifting</p>
    </div>
    
    <?php if ($message): ?>
        <div class="alert alert-<?= $messageType ?>">
            <?= htmlspecialchars($message) ?>
        </div>
    <?php endif; ?>
    
    <!-- Today's Statistics -->
    <div class="stats-grid">
        <?php
        $totalRecords = 0;
        $totalCalls = 0;
        $activePincodes = 0;
        
        if ($dailyStats->num_rows > 0) {
            while ($stat = $dailyStats->fetch_assoc()) {
                $totalRecords += $stat['total_records'];
                $totalCalls += $stat['total_calls'];
                $activePincodes++;
            }
        }
        ?>
        <div class="stat-card">
            <div class="stat-number"><?= $totalRecords ?></div>
            <div class="stat-label">Records Collected Today</div>
        </div>
        <div class="stat-card">
            <div class="stat-number"><?= $totalCalls ?></div>
            <div class="stat-label">API Calls Made Today</div>
        </div>
        <div class="stat-card">
            <div class="stat-number"><?= $activePincodes ?></div>
            <div class="stat-label">Active Pincodes Today</div>
        </div>
        <div class="stat-card">
            <div class="stat-number"><?= max(0, 500 - $totalRecords) ?></div>
            <div class="stat-label">Records Until Daily Limit</div>
        </div>
    </div>

    <!-- Create New Cron Job -->
    <div class="card">
        <h3>➕ Create New Cron Job</h3>
        <form method="POST">
            <input type="hidden" name="action" value="create_job">

            <div class="form-group">
                <label for="pincode">📍 Pincode:</label>
                <input type="text" name="pincode" id="pincode" required pattern="\d{6}"
                       placeholder="e.g., 110001, 400001, 560001">
            </div>

            <div class="form-group">
                <label for="area_name">🏘️ Area Name (Optional):</label>
                <input type="text" name="area_name" id="area_name"
                       placeholder="e.g., Connaught Place, Andheri West">
            </div>

            <div class="form-group">
                <label for="keywords">🏷️ Keywords:</label>
                <textarea name="keywords" id="keywords" rows="3"
                          placeholder="restaurant, shop, clinic, salon, medical, beauty, grocery, pharmacy"></textarea>
                <small style="color: #666;">Separate multiple keywords with commas</small>
            </div>

            <div class="form-group">
                <label for="daily_target">🎯 Daily Target:</label>
                <input type="number" name="daily_target" id="daily_target" value="500" min="1" max="1000">
                <small style="color: #666;">Maximum records to collect per day for this pincode</small>
            </div>

            <div class="form-group">
                <label for="run_frequency">⏰ Run Frequency:</label>
                <select name="run_frequency" id="run_frequency">
                    <option value="15">Every 15 minutes</option>
                    <option value="30" selected>Every 30 minutes</option>
                    <option value="60">Every 1 hour</option>
                    <option value="120">Every 2 hours</option>
                    <option value="180">Every 3 hours</option>
                    <option value="240">Every 4 hours</option>
                    <option value="360">Every 6 hours</option>
                    <option value="480">Every 8 hours</option>
                    <option value="720">Every 12 hours</option>
                    <option value="1440">Once daily</option>
                </select>
                <small style="color: #666;">How often should this cron job run automatically</small>
            </div>

            <button type="submit" class="btn btn-primary">Create Cron Job</button>
        </form>
    </div>

    <!-- Existing Cron Jobs -->
    <div class="card">
        <h3>📋 Existing Cron Jobs</h3>

        <?php if ($cronJobs->num_rows > 0): ?>
            <table class="jobs-table">
                <thead>
                    <tr>
                        <th>Pincode</th>
                        <th>Area</th>
                        <th>Keywords</th>
                        <th>Daily Target</th>
                        <th>Frequency</th>
                        <th>Status</th>
                        <th>Last Run</th>
                        <th>Total Collected</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php while ($job = $cronJobs->fetch_assoc()): ?>
                        <tr>
                            <td><strong><?= htmlspecialchars($job['pincode']) ?></strong></td>
                            <td><?= htmlspecialchars($job['area_name'] ?: 'N/A') ?></td>
                            <td><?= htmlspecialchars(substr($job['keywords'], 0, 50)) ?><?= strlen($job['keywords']) > 50 ? '...' : '' ?></td>
                            <td><?= $job['daily_target'] ?></td>
                            <td>
                                <?php
                                $frequency = $job['run_frequency'] ?? 30;
                                if ($frequency < 60) {
                                    echo $frequency . ' min';
                                } elseif ($frequency < 1440) {
                                    echo ($frequency / 60) . ' hr';
                                } else {
                                    echo 'Daily';
                                }
                                ?>
                            </td>
                            <td>
                                <span class="status-badge status-<?= $job['status'] ?>">
                                    <?= ucfirst($job['status']) ?>
                                </span>
                            </td>
                            <td><?= $job['last_run'] ? date('M j, H:i', strtotime($job['last_run'])) : 'Never' ?></td>
                            <td><?= $job['total_collected'] ?></td>
                            <td>
                                <!-- Manual Run Button -->
                                <form method="POST" style="display: inline;">
                                    <input type="hidden" name="action" value="run_manual">
                                    <input type="hidden" name="job_id" value="<?= $job['id'] ?>">
                                    <button type="submit" class="btn btn-success"
                                            onclick="return confirm('Run manual search for <?= htmlspecialchars($job['pincode']) ?>?')">
                                        ▶️ Run Now
                                    </button>
                                </form>

                                <!-- Toggle Status Button -->
                                <form method="POST" style="display: inline;">
                                    <input type="hidden" name="action" value="toggle_status">
                                    <input type="hidden" name="job_id" value="<?= $job['id'] ?>">
                                    <input type="hidden" name="new_status" value="<?= $job['status'] === 'active' ? 'inactive' : 'active' ?>">
                                    <button type="submit" class="btn <?= $job['status'] === 'active' ? 'btn-warning' : 'btn-success' ?>">
                                        <?= $job['status'] === 'active' ? '⏸️ Pause' : '▶️ Activate' ?>
                                    </button>
                                </form>
                            </td>
                        </tr>
                    <?php endwhile; ?>
                </tbody>
            </table>
        <?php else: ?>
            <div style="text-align: center; padding: 40px; color: #666;">
                <h4>📭 No Cron Jobs Created Yet</h4>
                <p>Create your first cron job above to start automated data collection.</p>
            </div>
        <?php endif; ?>
    </div>

    <!-- Instructions -->
    <div class="card">
        <h3>📖 How It Works</h3>
        <div style="line-height: 1.6;">
            <p><strong>🎯 Coordinate Shifting:</strong> Each search automatically shifts coordinates by 200m to discover different businesses in the same pincode area.</p>
            <p><strong>📊 Daily Limits:</strong> System tracks daily collection and stops at your target (default: 500 records) to stay within API limits.</p>
            <p><strong>🔄 Manual Execution:</strong> Click "Run Now" to manually execute a search with coordinate shifting for any configured pincode.</p>
            <p><strong>⚙️ Status Control:</strong> Pause/activate jobs as needed. Paused jobs won't run automatically.</p>
            <p><strong>📈 Progress Tracking:</strong> Monitor daily progress and total records collected per pincode.</p>
        </div>
    </div>

    <div class="card">
        <p><a href="admin.php" class="btn btn-secondary">← Back to Admin Panel</a></p>
    </div>
</div>

<?php include 'footer.php'; ?>
