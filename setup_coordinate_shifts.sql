-- Database setup for coordinate shifting system

-- Table to track coordinate shifts for each pincode
CREATE TABLE IF NOT EXISTS coordinate_shifts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    pincode VARCHAR(10) NOT NULL,
    shift_number INT NOT NULL DEFAULT 0,
    lat_offset DECIMAL(10, 8) NOT NULL DEFAULT 0,
    lng_offset DECIMAL(11, 8) NOT NULL DEFAULT 0,
    base_lat DECIMAL(10, 8) NOT NULL,
    base_lng DECIMAL(11, 8) NOT NULL,
    last_used TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY unique_pincode_shift (pincode, shift_number),
    INDEX idx_pincode (pincode),
    INDEX idx_last_used (last_used)
);

-- Table to track daily collection limits
CREATE TABLE IF NOT EXISTS daily_collection_stats (
    id INT AUTO_INCREMENT PRIMARY KEY,
    date DATE NOT NULL,
    pincode VARCHAR(10) NOT NULL,
    records_collected INT DEFAULT 0,
    api_calls_made INT DEFAULT 0,
    target_limit INT DEFAULT 500,
    status ENUM('active', 'target_reached', 'paused') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_date_pincode (date, pincode),
    INDEX idx_date (date),
    INDEX idx_pincode (pincode),
    INDEX idx_status (status)
);

-- Table to manage cron job configurations
CREATE TABLE IF NOT EXISTS cron_job_configs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    pincode VARCHAR(10) NOT NULL,
    area_name VARCHAR(255),
    keywords TEXT,
    daily_target INT DEFAULT 500,
    run_frequency INT DEFAULT 30 COMMENT 'Run frequency in minutes',
    status ENUM('active', 'inactive', 'target_reached') DEFAULT 'active',
    created_by VARCHAR(50),
    last_run TIMESTAMP NULL,
    total_collected INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_pincode (pincode),
    INDEX idx_status (status),
    INDEX idx_last_run (last_run)
);

-- Insert default coordinate shift patterns (200m grid around center)
-- These represent a systematic grid pattern around the base coordinates
INSERT IGNORE INTO coordinate_shifts (pincode, shift_number, lat_offset, lng_offset, base_lat, base_lng) VALUES
-- This will be populated dynamically for each pincode, but here's the pattern:
-- Shift 0: Center (0, 0)
-- Shift 1: North (0.0018, 0) - approximately 200m north
-- Shift 2: East (0, 0.0018) - approximately 200m east  
-- Shift 3: South (-0.0018, 0) - approximately 200m south
-- Shift 4: West (0, -0.0018) - approximately 200m west
-- Shift 5: Northeast (0.0018, 0.0018)
-- Shift 6: Southeast (-0.0018, 0.0018)
-- Shift 7: Southwest (-0.0018, -0.0018)
-- Shift 8: Northwest (0.0018, -0.0018)
-- And so on in expanding grid pattern...
('000000', 0, 0.0000, 0.0000, 0, 0),
('000000', 1, 0.0018, 0.0000, 0, 0),
('000000', 2, 0.0000, 0.0018, 0, 0),
('000000', 3, -0.0018, 0.0000, 0, 0),
('000000', 4, 0.0000, -0.0018, 0, 0),
('000000', 5, 0.0018, 0.0018, 0, 0),
('000000', 6, -0.0018, 0.0018, 0, 0),
('000000', 7, -0.0018, -0.0018, 0, 0),
('000000', 8, 0.0018, -0.0018, 0, 0);
