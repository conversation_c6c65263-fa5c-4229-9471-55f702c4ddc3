# Coordinate Shifting System - Implementation Guide

## 🎯 Overview
Enhanced the existing business search system with coordinate shifting functionality to collect ~500 daily records while staying within Google Places API free tier limits.

## ✅ What's Been Implemented

### 1. **Coordinate Shifting System** (`coordinate_shifter.php`)
- **200m Grid Pattern**: Systematic coordinate shifting in 200m increments
- **17 Shift Positions**: Center + 8 directions at 200m + 8 directions at 400m
- **Smart Rotation**: Uses least recently used shift positions
- **Base Coordinate Caching**: Stores pincode center coordinates to avoid repeated geocoding

### 2. **Enhanced Search System** (`fetch.php` - Modified)
- **Optional Coordinate Shifting**: Checkbox option in search form
- **Daily Limit Checking**: Stops when 500 records reached per day
- **Shift Information Display**: Shows which coordinate shift was used
- **Automatic Stats Tracking**: Updates daily collection counters

### 3. **Cron Job Management Interface** (`cron-manager.php`)
- **Job Configuration**: Set pincode, area, keywords, daily targets
- **Manual Execution**: "Run Now" button for immediate searches
- **Status Control**: Enable/disable jobs
- **Progress Monitoring**: Daily stats and total collection tracking
- **Admin-Only Access**: Restricted to admin users

### 4. **Manual Execution System** (`manual-fetch.php`)
- **Coordinate-Shifted Searches**: Uses next available coordinate shift
- **Real-time Results**: Shows immediate search results
- **Database Integration**: Saves results and updates job statistics
- **Progress Tracking**: Updates daily counters and job totals

### 5. **Database Structure** (`setup_coordinate_shifts.sql`)
```sql
-- Coordinate shift tracking
coordinate_shifts (id, pincode, shift_number, lat_offset, lng_offset, base_lat, base_lng, last_used)

-- Daily collection limits
daily_collection_stats (id, date, pincode, records_collected, api_calls_made, target_limit, status)

-- Cron job configurations  
cron_job_configs (id, pincode, area_name, keywords, daily_target, status, last_run, total_collected)
```

## 🚀 How to Use

### **Setup (One-time)**
1. **Run Database Setup**: Execute `setup_coordinate_shifts.sql` on your MySQL database
2. **File Permissions**: Ensure web server can read/write to all PHP files
3. **Admin Access**: Login with admin credentials (BM999 / Welcome@1)

### **Configure Cron Jobs**
1. Go to **Admin Panel** → **Cron Job Manager**
2. **Create New Job**:
   - Enter 6-digit pincode
   - Add area name (optional)
   - Set keywords (comma-separated)
   - Set daily target (default: 500)
3. **Save** - Job is created in 'active' status

### **Manual Execution**
1. In **Cron Manager**, find your job
2. Click **"▶️ Run Now"** button
3. System will:
   - Check daily limit
   - Get next coordinate shift (200m from previous)
   - Search Google Places API
   - Filter businesses without websites
   - Save to database
   - Update statistics

### **Monitor Progress**
- **Daily Stats**: View today's collection progress
- **Job Status**: See last run time and total collected
- **Coordinate Info**: Track which shifts have been used

## 📊 Expected Results

### **Daily Collection**
- **Target**: ~500 unique business records per day
- **Method**: 200m coordinate shifts every 30 minutes (if automated)
- **Coverage**: Systematic grid coverage of pincode areas
- **Efficiency**: Avoids duplicate results from same coordinates

### **API Usage Optimization**
- **Free Tier Compliance**: Stays within Google's daily limits
- **Smart Filtering**: Only saves businesses without websites
- **Duplicate Prevention**: Checks place_id before saving
- **Coordinate Variety**: Different results from each shift

## 🔧 Technical Features

### **Coordinate Shifting Logic**
```php
// 17 predefined shift patterns
Shift 0: Center (0, 0)
Shift 1-4: 200m in cardinal directions  
Shift 5-8: 200m in diagonal directions
Shift 9-12: 400m in cardinal directions
Shift 13-16: 400m in diagonal directions
```

### **Daily Limit Management**
- Tracks records collected per pincode per day
- Auto-stops when target reached
- Resets counters at midnight
- Configurable limits per job

### **Search Enhancement**
- **Regular Search**: Uses pincode center coordinates
- **Shifted Search**: Uses next available 200m shift
- **Visual Feedback**: Shows coordinate shift information
- **Progress Tracking**: Updates daily statistics

## 🎛️ Admin Controls

### **Job Management**
- **Create**: New cron jobs for different pincodes
- **Activate/Pause**: Control job execution
- **Monitor**: View progress and statistics
- **Manual Run**: Execute searches on-demand

### **Statistics Dashboard**
- Records collected today
- API calls made
- Active pincodes
- Records until daily limit

## 📁 Files Created/Modified

### **New Files**
- `coordinate_shifter.php` - Core shifting logic
- `cron-manager.php` - Management interface
- `manual-fetch.php` - Manual execution system
- `setup_coordinate_shifts.sql` - Database setup

### **Modified Files**
- `fetch.php` - Added coordinate shifting option
- `search.php` - Added coordinate shift checkbox
- `admin.php` - Added cron manager link

## 🔄 Next Steps (Future Enhancements)

1. **Automated Cron Jobs**: Set up actual cron jobs to run every 30 minutes
2. **Email Notifications**: Alert when daily targets are reached
3. **Export Features**: CSV export of collected data
4. **Advanced Filtering**: More business type filters
5. **Reporting Dashboard**: Weekly/monthly collection reports

## ✅ Ready for Testing

The system is now ready for testing on your live server. You can:
1. Run the database setup script
2. Login as admin and access the Cron Manager
3. Create test jobs and run manual searches
4. Monitor coordinate shifting and daily limits

The coordinate shifting system will help you systematically collect business data while staying within API limits and avoiding duplicate results!
