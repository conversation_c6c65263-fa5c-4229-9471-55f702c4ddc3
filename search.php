<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['user_logged_in']) || $_SESSION['user_logged_in'] !== true) {
    header("Location: login.php?redirect=search.php");
    exit;
}

$pageTitle = "Search Businesses - Business Finder";
include 'header.php';
?>

<style>
    .search-page-container {
        max-width: 800px;
        margin: 0 auto;
    }
    
    .search-header {
        text-align: center;
        margin-bottom: 40px;
    }
    
    .search-header h1 {
        color: #333;
        margin-bottom: 15px;
    }
    
    .search-header p {
        color: #666;
        font-size: 16px;
    }
    
    .search-container {
        background: white;
        padding: 40px;
        border-radius: 15px;
        box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        margin-bottom: 40px;
    }
    
    .form-group {
        margin-bottom: 25px;
    }
    
    .form-group label {
        display: block;
        margin-bottom: 8px;
        color: #333;
        font-weight: 500;
        font-size: 16px;
    }
    
    .form-group input {
        width: 100%;
        padding: 15px;
        border: 2px solid #ddd;
        border-radius: 8px;
        font-size: 16px;
        transition: border-color 0.3s;
    }
    
    .form-group input:focus {
        outline: none;
        border-color: #667eea;
    }
    
    .form-group small {
        display: block;
        color: #666;
        margin-top: 8px;
        font-size: 14px;
    }
    
    .search-button {
        width: 100%;
        padding: 15px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        border-radius: 8px;
        font-size: 18px;
        font-weight: 500;
        cursor: pointer;
        transition: transform 0.2s;
    }
    
    .search-button:hover {
        transform: translateY(-2px);
    }

    .checkbox-container {
        display: flex;
        align-items: center;
        cursor: pointer;
        font-weight: 500;
        color: #333;
        margin-bottom: 10px;
    }

    .checkbox-container input[type="checkbox"] {
        margin-right: 10px;
        transform: scale(1.2);
        cursor: pointer;
    }

    .search-tips {
        background: #f8f9fa;
        padding: 25px;
        border-radius: 10px;
        border-left: 4px solid #667eea;
    }
    
    .search-tips h3 {
        color: #333;
        margin-bottom: 15px;
    }
    
    .search-tips ul {
        color: #666;
        line-height: 1.8;
    }
    
    .search-tips li {
        margin-bottom: 8px;
    }
    
    .examples-section {
        background: white;
        padding: 30px;
        border-radius: 10px;
        box-shadow: 0 3px 15px rgba(0,0,0,0.1);
        margin-top: 30px;
    }
    
    .examples-section h3 {
        color: #333;
        margin-bottom: 20px;
        text-align: center;
    }
    
    .examples-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
    }
    
    .example-item {
        background: #f8f9fa;
        padding: 20px;
        border-radius: 8px;
        border-left: 4px solid #28a745;
    }
    
    .example-item h4 {
        color: #333;
        margin-bottom: 10px;
    }
    
    .example-item p {
        color: #666;
        font-size: 14px;
        margin-bottom: 10px;
    }
    
    .example-item .example-query {
        background: white;
        padding: 10px;
        border-radius: 5px;
        font-family: monospace;
        font-size: 13px;
        color: #495057;
    }
</style>

<div class="search-page-container">
    <div class="search-header">
        <h1>🔍 Business Search</h1>
        <p>Find businesses without websites using pincode and keyword combinations</p>
    </div>
    
    <div class="search-container">
        <form method="POST" action="fetch.php">
            <div class="form-group">
                <label for="pincode">📍 Enter Pincode:</label>
                <input type="text" name="pincode" id="pincode" required pattern="\d{6}" 
                       placeholder="e.g., 110001, 400001, 560001">
                <small>Enter a valid 6-digit Indian pincode</small>
            </div>
            
            <div class="form-group">
                <label for="keywords">🏷️ Enter Keywords (optional):</label>
                <input type="text" name="keywords" id="keywords" 
                       placeholder="e.g., restaurant, shop, clinic, salon">
                <small>
                    💡 Separate multiple keywords with commas. Leave empty to search all business types.
                </small>
            </div>

            <div class="form-group">
                <label class="checkbox-container">
                    <input type="checkbox" name="use_coordinate_shift" value="1" checked>
                    <span class="checkmark"></span>
                    🎯 Use Coordinate Shifting (Recommended for better coverage)
                </label>
                <small style="color: #666; margin-top: 5px; display: block;">
                    Shifts search coordinates by 200m to discover more businesses and avoid duplicate results from the same location.
                </small>
            </div>

            <button type="submit" class="search-button">🚀 Search Businesses</button>
        </form>
    </div>
    
    <div class="search-tips">
        <h3>🎯 Search Tips</h3>
        <ul>
            <li><strong>Specific Keywords:</strong> Use terms like "restaurant", "medical clinic", "beauty salon", "grocery store"</li>
            <li><strong>Multiple Keywords:</strong> Combine related terms: "restaurant, cafe, food, dining"</li>
            <li><strong>Broader Search:</strong> Leave keywords empty to find all business types in the area</li>
            <li><strong>Location:</strong> Use accurate pincodes for better results</li>
            <li><strong>Business Types:</strong> Try categories like "retail", "healthcare", "automotive", "services"</li>
        </ul>
    </div>
    
    <div class="examples-section">
        <h3>📋 Search Examples</h3>
        <div class="examples-grid">
            <div class="example-item">
                <h4>🍽️ Food & Dining</h4>
                <p>Find restaurants and food businesses</p>
                <div class="example-query">
                    Pincode: 110001<br>
                    Keywords: restaurant, cafe, food
                </div>
            </div>
            
            <div class="example-item">
                <h4>🏥 Healthcare</h4>
                <p>Discover medical and wellness services</p>
                <div class="example-query">
                    Pincode: 400001<br>
                    Keywords: clinic, doctor, medical
                </div>
            </div>
            
            <div class="example-item">
                <h4>💄 Beauty & Wellness</h4>
                <p>Find salons and beauty services</p>
                <div class="example-query">
                    Pincode: 560001<br>
                    Keywords: salon, spa, beauty
                </div>
            </div>
            
            <div class="example-item">
                <h4>🛍️ Retail & Shopping</h4>
                <p>Locate shops and retail stores</p>
                <div class="example-query">
                    Pincode: 600001<br>
                    Keywords: shop, store, retail
                </div>
            </div>
            
            <div class="example-item">
                <h4>🔧 Services</h4>
                <p>Find service-based businesses</p>
                <div class="example-query">
                    Pincode: 700001<br>
                    Keywords: repair, service, maintenance
                </div>
            </div>
            
            <div class="example-item">
                <h4>🏢 All Businesses</h4>
                <p>Search all business types</p>
                <div class="example-query">
                    Pincode: 500001<br>
                    Keywords: (leave empty)
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'footer.php'; ?>
