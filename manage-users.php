<?php
session_start();

// Check if user is logged in and is admin
if (!isset($_SESSION['user_logged_in']) || $_SESSION['user_logged_in'] !== true) {
    header("Location: login.php?redirect=manage-users.php");
    exit;
}

// Check if user has admin role
if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin') {
    header("Location: dashboard.php?error=access_denied");
    exit;
}

$pageTitle = "Manage Users - Business Finder";

// Load users
$usersFile = 'users.json';
$users = [];
$error = '';

if (file_exists($usersFile)) {
    $usersData = file_get_contents($usersFile);
    $users = json_decode($usersData, true) ?? [];
} else {
    $error = 'Users file not found.';
}

// Handle user status updates
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    $userId = $_POST['user_id'] ?? '';
    $action = $_POST['action'];
    
    if ($action === 'toggle_status' && !empty($userId)) {
        foreach ($users as &$user) {
            if ($user['id'] === $userId) {
                $user['status'] = ($user['status'] === 'active') ? 'inactive' : 'active';
                break;
            }
        }
        
        // Save updated users
        if (file_put_contents($usersFile, json_encode($users, JSON_PRETTY_PRINT))) {
            header("Location: manage-users.php?message=status_updated");
            exit;
        } else {
            $error = 'Failed to update user status.';
        }
    }
}

include 'header.php';
?>

<style>
    .users-container {
        max-width: 1000px;
        margin: 0 auto;
    }
    
    .page-header {
        background: white;
        padding: 30px;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin-bottom: 30px;
        text-align: center;
    }
    
    .page-header h1 {
        color: #333;
        margin-bottom: 15px;
    }
    
    .users-table-container {
        background: white;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        overflow: hidden;
        margin-bottom: 30px;
    }
    
    .users-table {
        width: 100%;
        border-collapse: collapse;
    }
    
    .users-table th {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 15px 10px;
        text-align: left;
        font-weight: 500;
    }
    
    .users-table td {
        padding: 15px 10px;
        border-bottom: 1px solid #eee;
        vertical-align: middle;
    }
    
    .users-table tr:hover {
        background-color: #f8f9fa;
    }
    
    .status-badge {
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 500;
        text-transform: uppercase;
    }
    
    .status-active {
        background-color: #d4edda;
        color: #155724;
    }
    
    .status-inactive {
        background-color: #f8d7da;
        color: #721c24;
    }
    
    .role-badge {
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 500;
        text-transform: uppercase;
    }
    
    .role-admin {
        background-color: #fff3cd;
        color: #856404;
    }
    
    .role-user {
        background-color: #cce5ff;
        color: #004085;
    }
    
    .btn {
        padding: 6px 12px;
        border: none;
        border-radius: 4px;
        font-size: 12px;
        cursor: pointer;
        text-decoration: none;
        display: inline-block;
        margin: 2px;
    }
    
    .btn-toggle {
        background-color: #ffc107;
        color: #212529;
    }
    
    .btn-toggle:hover {
        background-color: #e0a800;
    }
    
    .alert {
        padding: 12px;
        border-radius: 5px;
        margin-bottom: 20px;
    }
    
    .alert-success {
        background-color: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }
    
    .alert-error {
        background-color: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }
    
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }
    
    .stat-card {
        background: white;
        padding: 20px;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        text-align: center;
    }
    
    .stat-number {
        font-size: 2em;
        font-weight: bold;
        color: #667eea;
    }
    
    .stat-label {
        color: #666;
        margin-top: 5px;
    }
    
    .actions-bar {
        background: white;
        padding: 20px;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin-bottom: 30px;
        text-align: center;
    }
    
    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 12px 24px;
        text-decoration: none;
        border-radius: 8px;
        font-weight: 500;
        margin: 0 10px;
    }
    
    .btn-secondary {
        background: #6c757d;
        color: white;
        padding: 12px 24px;
        text-decoration: none;
        border-radius: 8px;
        font-weight: 500;
        margin: 0 10px;
    }
</style>

<div class="users-container">
    <div class="page-header">
        <h1>👥 User Management</h1>
        <p>Manage all user accounts in the system</p>
    </div>
    
    <?php if (isset($_GET['message']) && $_GET['message'] === 'status_updated'): ?>
        <div class="alert alert-success">
            User status updated successfully.
        </div>
    <?php endif; ?>
    
    <?php if ($error): ?>
        <div class="alert alert-error">
            <?= htmlspecialchars($error) ?>
        </div>
    <?php endif; ?>
    
    <!-- Statistics -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-number"><?= count($users) ?></div>
            <div class="stat-label">Total Users</div>
        </div>
        <div class="stat-card">
            <div class="stat-number"><?= count(array_filter($users, fn($u) => $u['status'] === 'active')) ?></div>
            <div class="stat-label">Active Users</div>
        </div>
        <div class="stat-card">
            <div class="stat-number"><?= count(array_filter($users, fn($u) => ($u['role'] ?? 'user') === 'admin')) ?></div>
            <div class="stat-label">Administrators</div>
        </div>
        <div class="stat-card">
            <div class="stat-number"><?= count(array_filter($users, fn($u) => ($u['role'] ?? 'user') === 'user')) ?></div>
            <div class="stat-label">Regular Users</div>
        </div>
    </div>
    
    <!-- Actions -->
    <div class="actions-bar">
        <a href="register.php" class="btn-primary">➕ Create New User</a>
        <a href="admin.php" class="btn-secondary">📊 Admin Panel</a>
        <a href="dashboard.php" class="btn-secondary">🏠 Dashboard</a>
    </div>
    
    <!-- Users Table -->
    <?php if (count($users) > 0): ?>
        <div class="users-table-container">
            <table class="users-table">
                <thead>
                    <tr>
                        <th>Username</th>
                        <th>Email</th>
                        <th>Role</th>
                        <th>Status</th>
                        <th>Created</th>
                        <th>Last Login</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($users as $user): ?>
                        <tr>
                            <td><strong><?= htmlspecialchars($user['username']) ?></strong></td>
                            <td><?= htmlspecialchars($user['email']) ?></td>
                            <td>
                                <span class="role-badge role-<?= $user['role'] ?? 'user' ?>">
                                    <?= ucfirst($user['role'] ?? 'user') ?>
                                </span>
                            </td>
                            <td>
                                <span class="status-badge status-<?= $user['status'] ?>">
                                    <?= ucfirst($user['status']) ?>
                                </span>
                            </td>
                            <td><?= date('M j, Y', strtotime($user['created_at'])) ?></td>
                            <td><?= $user['last_login'] ? date('M j, Y', strtotime($user['last_login'])) : 'Never' ?></td>
                            <td>
                                <?php if ($user['username'] !== $_SESSION['username']): ?>
                                    <form method="POST" style="display: inline;">
                                        <input type="hidden" name="user_id" value="<?= htmlspecialchars($user['id']) ?>">
                                        <input type="hidden" name="action" value="toggle_status">
                                        <button type="submit" class="btn btn-toggle" 
                                                onclick="return confirm('Toggle user status?')">
                                            <?= $user['status'] === 'active' ? 'Deactivate' : 'Activate' ?>
                                        </button>
                                    </form>
                                <?php else: ?>
                                    <span style="color: #666; font-size: 12px;">Current User</span>
                                <?php endif; ?>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    <?php else: ?>
        <div style="text-align: center; padding: 60px; background: white; border-radius: 10px;">
            <h3>No Users Found</h3>
            <p>No user accounts exist in the system.</p>
            <a href="register.php" class="btn-primary">Create First User</a>
        </div>
    <?php endif; ?>
</div>

<?php include 'footer.php'; ?>
