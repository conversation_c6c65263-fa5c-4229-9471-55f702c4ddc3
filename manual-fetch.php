<?php
session_start();

// Check if user is logged in and is admin
if (!isset($_SESSION['user_logged_in']) || $_SESSION['user_logged_in'] !== true) {
    header("Location: login.php?redirect=cron-manager.php");
    exit;
}

// Check if user has admin role
if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin') {
    header("Location: dashboard.php?error=access_denied");
    exit;
}

require 'config.php';
require 'coordinate_shifter.php';

// Database connection
$dbConnected = false;
$conn = null;

try {
    require 'db_connect.php';
    $dbConnected = true;
} catch (Exception $e) {
    die("Database connection failed: " . $e->getMessage());
}

$job_id = (int)($_GET['job_id'] ?? 0);

if ($job_id <= 0) {
    header("Location: cron-manager.php?error=invalid_job");
    exit;
}

// Get job details
$stmt = $conn->prepare("SELECT * FROM cron_job_configs WHERE id = ?");
$stmt->bind_param("i", $job_id);
$stmt->execute();
$result = $stmt->get_result();

if (!($job = $result->fetch_assoc())) {
    $stmt->close();
    header("Location: cron-manager.php?error=job_not_found");
    exit;
}
$stmt->close();

$pincode = $job['pincode'];
$keywords = $job['keywords'];
$apiKey = GOOGLE_API_KEY;

// Generate unique search ID for this manual run
$searchId = 'manual_' . date('Ymd_His') . '_' . $job_id;

// Initialize coordinate shifter
$coordinateShifter = new CoordinateShifter($conn, $apiKey);

// Check daily limit before proceeding
if ($coordinateShifter->isDailyLimitReached($pincode, $job['daily_target'])) {
    $pageTitle = "Daily Limit Reached - Business Finder";
    include 'header.php';
    echo '<div class="container"><div class="alert alert-warning">';
    echo '<h3>🎯 Daily Target Reached!</h3>';
    echo '<p>The daily collection limit of ' . $job['daily_target'] . ' records has been reached for pincode ' . htmlspecialchars($pincode) . '.</p>';
    echo '<p>Please try again tomorrow or adjust the daily target.</p>';
    echo '<p><a href="cron-manager.php">← Back to Cron Manager</a></p>';
    echo '</div></div>';
    include 'footer.php';
    exit;
}

// Database functions (same as fetch.php)
function saveBusinessResult($conn, $searchId, $pincode, $keywords, $businessData, $lat, $lng) {
    // First check if place_id already exists
    $checkStmt = $conn->prepare("SELECT id FROM business_search_results WHERE place_id = ?");
    $checkStmt->bind_param("s", $businessData['place_id']);
    $checkStmt->execute();
    $result = $checkStmt->get_result();

    if ($result->num_rows > 0) {
        // Place ID already exists, don't save again
        $checkStmt->close();
        return false; // Return false to indicate it was not saved (already exists)
    }

    $checkStmt->close();

    // Place ID doesn't exist, proceed with insert
    $stmt = $conn->prepare("INSERT INTO business_search_results (search_id, pincode, keywords, business_name, business_address, business_rating, business_phone, business_types, place_id, latitude, longitude) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");

    $rating = ($businessData['rating'] !== 'N/A') ? $businessData['rating'] : null;
    $phone = ($businessData['phone'] !== 'N/A') ? $businessData['phone'] : null;

    $stmt->bind_param("sssssssssdd",
        $searchId,
        $pincode,
        $keywords,
        $businessData['name'],
        $businessData['address'],
        $rating,
        $phone,
        $businessData['types'],
        $businessData['place_id'],
        $lat,
        $lng
    );

    $success = $stmt->execute();
    $stmt->close();
    return $success;
}

// Get shifted coordinates
try {
    $shiftedCoords = $coordinateShifter->getNextShiftCoordinates($pincode);
    $lat = $shiftedCoords['lat'];
    $lng = $shiftedCoords['lng'];
    $shiftInfo = $shiftedCoords['offset_info'];
} catch (Exception $e) {
    die("Error getting coordinates: " . $e->getMessage());
}

// Step 2: Get Nearby Businesses with Pagination
$radius = 2000; // in meters
$type = "store"; // general business type

$results = [];
$pageCount = 0;
$nextPageToken = null;
$savedCount = 0; // Track how many records were saved to database
$duplicateCount = 0; // Track how many duplicates were found

// Function to search by keywords if provided
function searchByKeywords($lat, $lng, $radius, $keywords, $apiKey) {
    $keywordResults = [];
    $keywordArray = array_map('trim', explode(',', $keywords));

    foreach ($keywordArray as $keyword) {
        if (empty($keyword)) continue;

        $query = urlencode($keyword);
        $textSearchUrl = "https://maps.googleapis.com/maps/api/place/textsearch/json?query={$query}&location={$lat},{$lng}&radius={$radius}&key={$apiKey}";
        $textSearchData = json_decode(file_get_contents($textSearchUrl), true);

        if (isset($textSearchData['results'])) {
            $keywordResults = array_merge($keywordResults, $textSearchData['results']);
        }

        // Small delay to avoid hitting API limits
        usleep(100000); // 0.1 second delay
    }

    return $keywordResults;
}

// Combine results from both nearby search and keyword search
$allPlaces = [];

// If keywords are provided, search by keywords
if (!empty($keywords)) {
    $keywordPlaces = searchByKeywords($lat, $lng, $radius, $keywords, $apiKey);
    $allPlaces = array_merge($allPlaces, $keywordPlaces);
}

// Also do nearby search for general businesses
do {
    if ($nextPageToken) {
        sleep(4); // required delay for next_page_token
        $placesUrl = "https://maps.googleapis.com/maps/api/place/nearbysearch/json?pagetoken=$nextPageToken&key=$apiKey";
    } else {
        $placesUrl = "https://maps.googleapis.com/maps/api/place/nearbysearch/json?location={$lat},{$lng}&radius={$radius}&type={$type}&key=$apiKey";
    }

    $placesData = json_decode(file_get_contents($placesUrl), true);

    if (!isset($placesData['results'])) break;

    // Add nearby search results to all places
    $allPlaces = array_merge($allPlaces, $placesData['results']);

    $nextPageToken = $placesData['next_page_token'] ?? null;
    $pageCount++;

    // Limit to prevent excessive API calls
    if ($pageCount >= 3) break;

} while ($nextPageToken);

// Remove duplicates based on place_id
$uniquePlaces = [];
$seenPlaceIds = [];

foreach ($allPlaces as $place) {
    if (!in_array($place['place_id'], $seenPlaceIds)) {
        $uniquePlaces[] = $place;
        $seenPlaceIds[] = $place['place_id'];
    }
}

// Step 3: Process all unique places and filter those without websites
foreach ($uniquePlaces as $place) {
    $placeId = $place['place_id'];

    // Fetch details (for phone number, website)
    $detailsUrl = "https://maps.googleapis.com/maps/api/place/details/json?place_id={$placeId}&fields=name,formatted_address,rating,website,formatted_phone_number,types&key={$apiKey}";
    $detailsData = json_decode(file_get_contents($detailsUrl), true);

    if (isset($detailsData['result'])) {
        $details = $detailsData['result'];

        // Skip businesses that have websites
        if (isset($details['website']) && !empty($details['website'])) {
            continue;
        }

        $businessData = [
            'name' => $details['name'] ?? 'N/A',
            'address' => $details['formatted_address'] ?? 'N/A',
            'rating' => $details['rating'] ?? 'N/A',
            'phone' => $details['formatted_phone_number'] ?? 'N/A',
            'types' => isset($details['types']) ? implode(', ', $details['types']) : 'N/A',
            'place_id' => $placeId
        ];

        $results[] = $businessData;

        // Save to database if connected
        if ($dbConnected && $conn) {
            try {
                $saveResult = saveBusinessResult($conn, $searchId, $pincode, $keywords, $businessData, $lat, $lng);
                if ($saveResult) {
                    $savedCount++;
                } else {
                    // saveBusinessResult returns false if place_id already exists
                    $duplicateCount++;
                }
            } catch (Exception $e) {
                // Continue processing even if database save fails
            }
        }
    }

    // Small delay to avoid hitting API limits
    usleep(50000); // 0.05 second delay
}

// Update daily stats and job info
$coordinateShifter->updateDailyStats($pincode, $savedCount, 1);

// Update job's last run time and total collected
$stmt = $conn->prepare("UPDATE cron_job_configs SET last_run = NOW(), total_collected = total_collected + ? WHERE id = ?");
$stmt->bind_param("ii", $savedCount, $job_id);
$stmt->execute();
$stmt->close();

$pageTitle = "Manual Search Results - Business Finder";
include 'header.php';
?>

<style>
    .results-container {
        max-width: 1200px;
        margin: 0 auto;
    }

    .search-info {
        background: white;
        padding: 25px;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin-bottom: 30px;
    }

    .search-info h2 {
        color: #333;
        margin-bottom: 20px;
    }

    .search-params {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin-bottom: 20px;
    }

    .param-item {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 8px;
        border-left: 4px solid #007cba;
    }

    .param-label {
        font-weight: 600;
        color: #333;
        margin-bottom: 5px;
    }

    .param-value {
        color: #666;
        font-size: 14px;
    }

    .results-table-container {
        background: white;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        overflow: hidden;
    }

    .results-table {
        width: 100%;
        border-collapse: collapse;
    }

    .results-table th {
        background: #f8f9fa;
        padding: 15px;
        text-align: left;
        font-weight: 600;
        color: #333;
        border-bottom: 2px solid #dee2e6;
    }

    .results-table td {
        padding: 15px;
        border-bottom: 1px solid #dee2e6;
        vertical-align: top;
    }

    .results-table tr:hover {
        background: #f8f9fa;
    }

    .no-results {
        text-align: center;
        padding: 60px 20px;
        background: white;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .back-link {
        text-align: center;
        margin-top: 30px;
    }

    .back-link a {
        color: #007cba;
        text-decoration: none;
        font-weight: 500;
    }

    .back-link a:hover {
        text-decoration: underline;
    }

    .alert {
        padding: 15px;
        margin-bottom: 20px;
        border-radius: 8px;
        font-weight: 500;
    }

    .alert-success {
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }
</style>

<div class="results-container">
    <div class="search-info">
        <h2>🔍 Manual Search Results</h2>
        <div class="search-params">
            <div class="param-item">
                <div class="param-label">📍 Pincode</div>
                <div class="param-value"><?= htmlspecialchars($pincode) ?></div>
            </div>
            <div class="param-item">
                <div class="param-label">🏘️ Area</div>
                <div class="param-value"><?= htmlspecialchars($job['area_name'] ?: 'Not specified') ?></div>
            </div>
            <div class="param-item">
                <div class="param-label">🏷️ Keywords</div>
                <div class="param-value">
                    <?= !empty($keywords) ? htmlspecialchars($keywords) : 'All business types' ?>
                </div>
            </div>
            <div class="param-item">
                <div class="param-label">📊 Results Found</div>
                <div class="param-value"><?= count($results) ?> businesses</div>
            </div>
            <div class="param-item">
                <div class="param-label">🆔 Search ID</div>
                <div class="param-value"><?= htmlspecialchars($searchId) ?></div>
            </div>
            <div class="param-item">
                <div class="param-label">📍 Coordinates</div>
                <div class="param-value">
                    <span style="color: #007cba;"><?= htmlspecialchars($shiftInfo) ?></span>
                    <br><small style="color: #666;">Lat: <?= number_format($lat, 6) ?>, Lng: <?= number_format($lng, 6) ?></small>
                </div>
            </div>
            <div class="param-item">
                <div class="param-label">💾 Database</div>
                <div class="param-value">
                    <span style="color: green;">✓ Saved <?= $savedCount ?> new records</span>
                    <?php if ($duplicateCount > 0): ?>
                        <br><small style="color: #666;"><?= $duplicateCount ?> duplicates skipped</small>
                    <?php endif; ?>
                </div>
            </div>
            <div class="param-item">
                <div class="param-label">🎯 Daily Progress</div>
                <div class="param-value">
                    <?php
                    $dailyStats = $coordinateShifter->getDailyStats($pincode);
                    echo $dailyStats['records_collected'] . ' / ' . $job['daily_target'] . ' records';
                    ?>
                </div>
            </div>
        </div>

        <?php if ($savedCount > 0): ?>
            <div class="alert alert-success">
                🎉 Successfully collected <?= $savedCount ?> new business records with coordinate shifting!
            </div>
        <?php endif; ?>
    </div>

    <?php if (count($results) === 0): ?>
        <div class="no-results">
            <h3>🔍 No New Results Found</h3>
            <p>No businesses without websites were found in this coordinate shift area.</p>
            <p>This could mean:</p>
            <ul style="text-align: left; display: inline-block;">
                <li>All businesses in this area already have websites</li>
                <li>This coordinate shift area has been searched before</li>
                <li>The search keywords are too specific</li>
            </ul>
        </div>
    <?php else: ?>
        <div class="results-table-container">
            <table class="results-table">
                <thead>
                    <tr>
                        <th>🏢 Business Name</th>
                        <th>📍 Address</th>
                        <th>⭐ Rating</th>
                        <th>📞 Phone</th>
                        <th>🏷️ Business Types</th>
                        <th>📅 Found Date & Time</th>
                        <th>🆔 Place ID</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($results as $row): ?>
                        <tr>
                            <td><strong><?= htmlspecialchars($row['name']) ?></strong></td>
                            <td><?= htmlspecialchars($row['address']) ?></td>
                            <td><?= $row['rating'] !== 'N/A' ? '⭐ ' . $row['rating'] : 'No rating' ?></td>
                            <td><?= $row['phone'] !== 'N/A' ? htmlspecialchars($row['phone']) : 'Not available' ?></td>
                            <td><?= htmlspecialchars($row['types']) ?></td>
                            <td>
                                <?= date('M j, Y') ?><br>
                                <small style="color: #666;"><?= date('H:i:s') ?></small>
                            </td>
                            <td><small><?= htmlspecialchars($row['place_id']) ?></small></td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    <?php endif; ?>

    <div class="back-link">
        <p>
            <a href="cron-manager.php">← Back to Cron Manager</a> |
            <a href="view-results.php">View All Results</a> |
            <a href="admin.php">Admin Panel</a>
        </p>
    </div>
</div>

<?php include 'footer.php'; ?>
