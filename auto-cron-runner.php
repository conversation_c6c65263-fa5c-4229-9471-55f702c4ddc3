<?php
/**
 * Auto Cron Runner Script
 * This script should be called by server cron every 15 minutes
 * It checks which jobs need to run based on their frequency and last run time
 */

// Prevent direct browser access
if (php_sapi_name() !== 'cli' && !isset($_GET['manual_run'])) {
    // Allow manual testing with ?manual_run=1
    if (!isset($_GET['manual_run'])) {
        die("This script should only be run via cron job or with manual_run parameter");
    }
}

require_once 'db_connect.php';
require_once 'coordinate_shifter.php';
require_once 'config.php';

// Log function for debugging
function logMessage($message) {
    $timestamp = date('Y-m-d H:i:s');
    $logEntry = "[$timestamp] $message" . PHP_EOL;
    
    // Log to file
    file_put_contents('cron-runner.log', $logEntry, FILE_APPEND | LOCK_EX);
    
    // Also output to console/browser for debugging
    echo $logEntry;
}

logMessage("=== Auto Cron Runner Started ===");

try {
    // Get all active cron jobs
    $query = "SELECT * FROM cron_job_configs WHERE status = 'active' ORDER BY last_run ASC";
    $result = $conn->query($query);
    
    if (!$result) {
        throw new Exception("Failed to fetch cron jobs: " . $conn->error);
    }
    
    $jobsProcessed = 0;
    $jobsSkipped = 0;
    $totalRecordsCollected = 0;
    
    logMessage("Found " . $result->num_rows . " active cron jobs");
    
    while ($job = $result->fetch_assoc()) {
        $jobId = $job['id'];
        $pincode = $job['pincode'];
        $areaName = $job['area_name'] ?: 'N/A';
        $keywords = $job['keywords'];
        $dailyTarget = $job['daily_target'];
        $frequency = $job['run_frequency']; // in minutes
        $lastRun = $job['last_run'];
        
        logMessage("Processing job ID $jobId: $pincode ($areaName) - Frequency: {$frequency} min");
        
        // Check if job should run based on frequency
        $shouldRun = false;
        $currentTime = time();
        
        if (empty($lastRun)) {
            // Never run before, should run now
            $shouldRun = true;
            logMessage("Job never run before - scheduling for execution");
        } else {
            // Check if enough time has passed since last run
            $lastRunTime = strtotime($lastRun);
            $timeDiff = ($currentTime - $lastRunTime) / 60; // difference in minutes
            
            if ($timeDiff >= $frequency) {
                $shouldRun = true;
                logMessage("Time since last run: " . round($timeDiff, 1) . " min - scheduling for execution");
            } else {
                $timeRemaining = $frequency - $timeDiff;
                logMessage("Too soon to run - " . round($timeRemaining, 1) . " minutes remaining");
            }
        }
        
        if (!$shouldRun) {
            $jobsSkipped++;
            continue;
        }
        
        // Initialize coordinate shifter
        $coordinateShifter = new CoordinateShifter($conn, GOOGLE_API_KEY);
        
        // Check daily limit
        if ($coordinateShifter->isDailyLimitReached($pincode, $dailyTarget)) {
            logMessage("Daily limit reached for $pincode - skipping");
            
            // Update job status to target_reached
            $updateStmt = $conn->prepare("UPDATE cron_job_configs SET status = 'target_reached' WHERE id = ?");
            $updateStmt->bind_param("i", $jobId);
            $updateStmt->execute();
            $updateStmt->close();
            
            $jobsSkipped++;
            continue;
        }
        
        // Execute the job
        logMessage("Executing job for $pincode...");
        
        try {
            // Get shifted coordinates
            $shiftedCoords = $coordinateShifter->getNextShiftCoordinates($pincode);
            $lat = $shiftedCoords['lat'];
            $lng = $shiftedCoords['lng'];
            $shiftInfo = $shiftedCoords['offset_info'];
            
            logMessage("Using coordinates: Lat $lat, Lng $lng ($shiftInfo)");
            
            // Generate search ID
            $searchId = 'auto_' . date('Ymd_His') . '_' . $jobId;
            
            // Execute search (similar to manual-fetch.php logic)
            $recordsCollected = executeSearch($conn, $searchId, $pincode, $keywords, $lat, $lng, $coordinateShifter);
            
            // Update job statistics
            $updateStmt = $conn->prepare("UPDATE cron_job_configs SET last_run = NOW(), total_collected = total_collected + ? WHERE id = ?");
            $updateStmt->bind_param("ii", $recordsCollected, $jobId);
            $updateStmt->execute();
            $updateStmt->close();
            
            $totalRecordsCollected += $recordsCollected;
            $jobsProcessed++;
            
            logMessage("Job completed - collected $recordsCollected new records");
            
            // Small delay between jobs to avoid API rate limits
            sleep(2);
            
        } catch (Exception $e) {
            logMessage("Error executing job $jobId: " . $e->getMessage());
        }
    }
    
    logMessage("=== Auto Cron Runner Completed ===");
    logMessage("Jobs processed: $jobsProcessed");
    logMessage("Jobs skipped: $jobsSkipped");
    logMessage("Total records collected: $totalRecordsCollected");
    
} catch (Exception $e) {
    logMessage("FATAL ERROR: " . $e->getMessage());
} finally {
    if (isset($conn)) {
        $conn->close();
    }
}

/**
 * Execute search for a specific job
 */
function executeSearch($conn, $searchId, $pincode, $keywords, $lat, $lng, $coordinateShifter) {
    $apiKey = GOOGLE_API_KEY;
    $radius = 2000;
    $type = "store";
    
    $results = [];
    $savedCount = 0;
    $duplicateCount = 0;
    
    // Search by keywords if provided
    $allPlaces = [];
    if (!empty($keywords)) {
        $keywordPlaces = searchByKeywords($lat, $lng, $radius, $keywords, $apiKey);
        $allPlaces = array_merge($allPlaces, $keywordPlaces);
    }
    
    // Also do nearby search
    $nextPageToken = null;
    $pageCount = 0;
    
    do {
        if ($nextPageToken) {
            sleep(4);
            $placesUrl = "https://maps.googleapis.com/maps/api/place/nearbysearch/json?pagetoken=$nextPageToken&key=$apiKey";
        } else {
            $placesUrl = "https://maps.googleapis.com/maps/api/place/nearbysearch/json?location={$lat},{$lng}&radius={$radius}&type={$type}&key=$apiKey";
        }
        
        $placesData = json_decode(file_get_contents($placesUrl), true);
        
        if (!isset($placesData['results'])) break;
        
        $allPlaces = array_merge($allPlaces, $placesData['results']);
        $nextPageToken = $placesData['next_page_token'] ?? null;
        $pageCount++;
        
    } while ($nextPageToken && $pageCount < 3);
    
    // Remove duplicates
    $uniquePlaces = [];
    $seenPlaceIds = [];
    
    foreach ($allPlaces as $place) {
        if (!in_array($place['place_id'], $seenPlaceIds)) {
            $uniquePlaces[] = $place;
            $seenPlaceIds[] = $place['place_id'];
        }
    }
    
    // Process places and filter those without websites
    foreach ($uniquePlaces as $place) {
        $placeId = $place['place_id'];
        
        $detailsUrl = "https://maps.googleapis.com/maps/api/place/details/json?place_id={$placeId}&fields=name,formatted_address,rating,website,formatted_phone_number,types&key=$apiKey";
        $detailsData = json_decode(file_get_contents($detailsUrl), true);
        
        if (isset($detailsData['result'])) {
            $details = $detailsData['result'];
            
            // Skip businesses with websites
            if (isset($details['website']) && !empty($details['website'])) {
                continue;
            }
            
            $businessData = [
                'name' => $details['name'] ?? 'N/A',
                'address' => $details['formatted_address'] ?? 'N/A',
                'rating' => $details['rating'] ?? 'N/A',
                'phone' => $details['formatted_phone_number'] ?? 'N/A',
                'types' => isset($details['types']) ? implode(', ', $details['types']) : 'N/A',
                'place_id' => $placeId
            ];
            
            // Save to database
            if (saveBusinessResult($conn, $searchId, $pincode, $keywords, $businessData, $lat, $lng)) {
                $savedCount++;
            } else {
                $duplicateCount++;
            }
        }
        
        usleep(50000); // Small delay
    }
    
    // Update daily stats
    $coordinateShifter->updateDailyStats($pincode, $savedCount, 1);
    
    return $savedCount;
}

/**
 * Search by keywords
 */
function searchByKeywords($lat, $lng, $radius, $keywords, $apiKey) {
    $keywordResults = [];
    $keywordArray = array_map('trim', explode(',', $keywords));
    
    foreach ($keywordArray as $keyword) {
        if (empty($keyword)) continue;
        
        $query = urlencode($keyword);
        $textSearchUrl = "https://maps.googleapis.com/maps/api/place/textsearch/json?query={$query}&location={$lat},{$lng}&radius={$radius}&key={$apiKey}";
        $textSearchData = json_decode(file_get_contents($textSearchUrl), true);
        
        if (isset($textSearchData['results'])) {
            $keywordResults = array_merge($keywordResults, $textSearchData['results']);
        }
        
        usleep(100000);
    }
    
    return $keywordResults;
}

/**
 * Save business result to database
 */
function saveBusinessResult($conn, $searchId, $pincode, $keywords, $businessData, $lat, $lng) {
    // Check if place_id already exists
    $checkStmt = $conn->prepare("SELECT id FROM business_search_results WHERE place_id = ?");
    $checkStmt->bind_param("s", $businessData['place_id']);
    $checkStmt->execute();
    $result = $checkStmt->get_result();
    
    if ($result->num_rows > 0) {
        $checkStmt->close();
        return false;
    }
    
    $checkStmt->close();
    
    // Insert new record
    $stmt = $conn->prepare("INSERT INTO business_search_results (search_id, pincode, keywords, business_name, business_address, business_rating, business_phone, business_types, place_id, latitude, longitude) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
    
    $rating = ($businessData['rating'] !== 'N/A') ? $businessData['rating'] : null;
    $phone = ($businessData['phone'] !== 'N/A') ? $businessData['phone'] : null;
    
    $stmt->bind_param("sssssssssdd",
        $searchId,
        $pincode,
        $keywords,
        $businessData['name'],
        $businessData['address'],
        $rating,
        $phone,
        $businessData['types'],
        $businessData['place_id'],
        $lat,
        $lng
    );
    
    $success = $stmt->execute();
    $stmt->close();
    return $success;
}
?>
